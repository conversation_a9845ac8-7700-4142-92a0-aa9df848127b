import os
import re
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False

# 全局变量存储同缆组配置
CABLE_GROUPS = {}

def load_cable_groups_from_json(config_file="cable_groups.json"):
    """从JSON配置文件加载同缆组"""
    global CABLE_GROUPS
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
            # 将字符串键转换为整数键
            CABLE_GROUPS = {int(k): v for k, v in config['cable_groups'].items()}
        print(f"成功从 {config_file} 加载了 {len(CABLE_GROUPS)} 个同缆组")
        return True
    except FileNotFoundError:
        print(f"配置文件 {config_file} 未找到")
        return False
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return False

def load_cable_groups_from_csv(config_file="cable_groups.csv"):
    """从CSV配置文件加载同缆组"""
    global CABLE_GROUPS
    try:
        df = pd.read_csv(config_file)
        CABLE_GROUPS = {}
        for _, row in df.iterrows():
            group_id = int(row['group_id'])
            filename = row['filename']
            if group_id not in CABLE_GROUPS:
                CABLE_GROUPS[group_id] = []
            CABLE_GROUPS[group_id].append(filename)
        print(f"成功从 {config_file} 加载了 {len(CABLE_GROUPS)} 个同缆组")
        return True
    except FileNotFoundError:
        print(f"配置文件 {config_file} 未找到")
        return False
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return False

def save_cable_groups_to_json(config_file="cable_groups.json"):
    """将当前同缆组保存到JSON配置文件"""
    try:
        config = {
            "cable_groups": {str(k): v for k, v in CABLE_GROUPS.items()}
        }
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print(f"同缆组配置已保存到 {config_file}")
        return True
    except Exception as e:
        print(f"保存配置文件失败: {e}")
        return False

def save_cable_groups_to_csv(config_file="cable_groups.csv"):
    """将当前同缆组保存到CSV配置文件"""
    try:
        data = []
        for group_id, filenames in CABLE_GROUPS.items():
            for filename in filenames:
                data.append({'group_id': group_id, 'filename': filename})
        df = pd.DataFrame(data)
        df.to_csv(config_file, index=False)
        print(f"同缆组配置已保存到 {config_file}")
        return True
    except Exception as e:
        print(f"保存配置文件失败: {e}")
        return False

def get_cable_group(filename):
    """获取文件所属的同缆组"""
    for group_id, files in CABLE_GROUPS.items():
        if filename in files:
            return group_id
    return -1  # 返回-1表示不属于任何已知组

def detect_blind_zone_end(data, window_size=20, stability_threshold=0.5):
    """检测盲区结束位置

    Args:
        data: OTDR数据数组 [位置, 数值]
        window_size: 滑动窗口大小
        stability_threshold: 稳定性阈值（标准差）

    Returns:
        int: 盲区结束的索引位置
    """
    if len(data) < window_size * 2:
        return min(10, len(data) // 4)  # 如果数据太短，返回保守值

    values = data[:, 1]

    # 寻找信号稳定的起始点
    for i in range(window_size, len(values) - window_size):
        window = values[i-window_size:i+window_size]
        if np.std(window) < stability_threshold:
            return max(i - window_size, 0)

    # 如果没找到稳定区域，使用梯度变化检测
    gradients = np.abs(np.gradient(values))
    smooth_gradients = np.convolve(gradients, np.ones(window_size)/window_size, mode='valid')

    # 找到梯度变化较小的起始点
    threshold = np.percentile(smooth_gradients, 25)  # 使用25分位数作为阈值
    stable_indices = np.where(smooth_gradients < threshold)[0]

    if len(stable_indices) > 0:
        return min(stable_indices[0] + window_size//2, len(data) // 4)

    # 默认返回数据长度的1/10或最多20个点
    return min(20, len(data) // 10)

def detect_noise_start(data, window_size=30, noise_threshold=2.0):
    """检测尾部噪声开始位置

    Args:
        data: OTDR数据数组 [位置, 数值]
        window_size: 滑动窗口大小
        noise_threshold: 噪声检测阈值（标准差倍数）

    Returns:
        int: 噪声开始的索引位置（从末尾算起）
    """
    if len(data) < window_size * 2:
        return min(50, len(data) // 4)  # 如果数据太短，返回保守值

    values = data[:, 1]

    # 从后往前检测噪声
    for i in range(len(values) - window_size, window_size, -1):
        window = values[i:i+window_size]
        window_std = np.std(window)

        # 计算前面一段数据的标准差作为基准
        baseline_window = values[max(0, i-window_size*2):i]
        if len(baseline_window) > 0:
            baseline_std = np.std(baseline_window)

            # 如果当前窗口的标准差明显大于基准，认为是噪声开始
            if window_std > baseline_std * noise_threshold:
                return len(data) - i

    # 使用信号强度检测：如果尾部信号强度明显下降，可能是噪声
    # 计算信号的移动平均
    moving_avg = np.convolve(values, np.ones(window_size)/window_size, mode='valid')

    # 检测信号强度的显著下降
    if len(moving_avg) > window_size:
        # 计算后半段相对于前半段的信号强度变化
        mid_point = len(moving_avg) // 2
        front_avg = np.mean(moving_avg[:mid_point])
        back_avg = np.mean(moving_avg[mid_point:])

        # 如果后半段信号明显弱于前半段，从信号下降点开始切除
        if front_avg - back_avg > np.std(moving_avg):
            decline_start = np.where(moving_avg < (front_avg + back_avg) / 2)[0]
            if len(decline_start) > 0:
                return len(data) - (decline_start[0] + window_size//2)

    # 默认返回数据长度的1/8或最多80个点
    return min(80, len(data) // 8)

def process_otdr_file(file_path, normalize=True, adaptive_trimming=True):
    """处理单个OTDR文件,智能去除首尾异常点并进行Min-Max归一化

    Args:
        file_path: OTDR文件路径
        normalize: 是否进行Min-Max归一化，默认为True
        adaptive_trimming: 是否使用自适应裁剪，默认为True

    Returns:
        numpy.ndarray: 处理后的数据，包含位置和数值两列
    """
    with open(file_path, 'r') as f:
        content = f.read()

    # 提取数据点（跳过非数字行）
    data_lines = []
    in_data_section = False
    for line in content.split('\n'):
        if "POS(km)" in line:
            in_data_section = True
            continue
        if in_data_section and line.strip():
            if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):
                data_lines.append(line)

    # 转换为数值
    data = []
    for line in data_lines:
        pos, val = map(float, line.strip().split())
        data.append([pos, val])

    data = np.array(data)

    if len(data) == 0:
        return data

    # 智能去除首尾异常点
    if adaptive_trimming:
        # 检测盲区结束位置
        blind_zone_end = detect_blind_zone_end(data)

        # 检测尾部噪声开始位置
        noise_start = detect_noise_start(data)

        # 应用裁剪，确保不会过度裁剪
        start_idx = min(blind_zone_end, len(data) // 4)
        end_idx = max(len(data) - noise_start, len(data) * 3 // 4)

        if start_idx < end_idx:
            data = data[start_idx:end_idx]
        else:
            # 如果计算出的范围不合理，使用保守的固定值
            start_idx = min(10, len(data) // 10)
            end_idx = max(len(data) - 50, len(data) * 9 // 10)
            data = data[start_idx:end_idx]
    else:
        # 使用原来的固定方式
        if len(data) > 10:
            data = data[10:]
        if len(data) > 50:
            data = data[:-50]

    # Min-Max数据归一化
    if normalize and len(data) > 0:
        values = data[:, 1]  # 获取数值列
        min_val = np.min(values)
        max_val = np.max(values)

        # 避免除零错误
        if max_val != min_val:
            # 应用Min-Max归一化公式: x_n = (x - min(x)) / (max(x) - min(x))
            data[:, 1] = (values - min_val) / (max_val - min_val)
        else:
            # 如果所有值相同，归一化为0
            data[:, 1] = 0

    return data

def process_folder(folder_path, normalize=True, adaptive_trimming=True):
    """处理整个文件夹及其子文件夹的OTDR文件

    Args:
        folder_path: 根文件夹路径
        normalize: 是否进行Min-Max归一化，默认为True
        adaptive_trimming: 是否使用自适应裁剪，默认为True

    Returns:
        dict: 以文件名为键，数据为值的字典
    """
    file_dict = {}
    processed_count = 0

    def process_single_file(file_path):
        """处理单个文件并返回数据"""
        try:
            data = process_otdr_file(file_path, normalize=normalize, adaptive_trimming=adaptive_trimming)
            if len(data) > 0:
                return data
        except Exception as e:
            print(f"处理文件失败: {os.path.basename(file_path)} - {str(e)}")
        return None

    def walk_folder(current_path):
        """递归遍历文件夹"""
        nonlocal processed_count
        for item in os.listdir(current_path):
            item_path = os.path.join(current_path, item)

            relative_path = os.path.relpath(os.path.dirname(item_path), folder_path)
            current_prefix = relative_path.replace("\\", "_").replace("/", "_")
            if current_prefix == ".":
                current_prefix = ""

            if os.path.isfile(item_path) and item.endswith(".asc"):
                data = process_single_file(item_path)
                if data is not None:
                    new_filename = f"{current_prefix}_{item}" if current_prefix else item
                    file_dict[new_filename] = data
                    processed_count += 1

            elif os.path.isdir(item_path):
                walk_folder(item_path)

    print(f"开始处理文件夹: {folder_path}")
    walk_folder(folder_path)
    print(f"成功处理 {processed_count} 个OTDR文件")

    return file_dict

def plot_all_curves(file_dict, title_suffix=""):
    """绘制所有曲线在同一图中"""
    plt.figure(figsize=(12, 6))

    # 直接绘制所有曲线
    for filename, data in file_dict.items():
        plt.plot(data[:, 0], data[:, 1],
                linewidth=1.5,
                alpha=0.7,
                label=filename)

    plt.title(f"OTDR曲线综合比对{title_suffix}", pad=20)
    plt.xlabel("Position (km)")
    plt.ylabel("Value (dB)" if not title_suffix else "Normalized Value")
    plt.grid(True, alpha=0.3)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout()
    plt.show()

def plot_comparison(original_data, normalized_data):
    """对比显示原始数据和归一化后的数据"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

    # 绘制原始数据
    for filename, data in original_data.items():
        ax1.plot(data[:, 0], data[:, 1],
                linewidth=1.5,
                alpha=0.7,
                label=filename)

    ax1.set_title("原始OTDR曲线（去除首尾异常点）", pad=20)
    ax1.set_xlabel("Position (km)")
    ax1.set_ylabel("Value (dB)")
    ax1.grid(True, alpha=0.3)
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    # 绘制归一化数据
    for filename, data in normalized_data.items():
        ax2.plot(data[:, 0], data[:, 1],
                linewidth=1.5,
                alpha=0.7,
                label=filename)

    ax2.set_title("Min-Max归一化后的OTDR曲线", pad=20)
    ax2.set_xlabel("Position (km)")
    ax2.set_ylabel("Normalized Value [0,1]")
    ax2.grid(True, alpha=0.3)
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    plt.tight_layout()
    plt.show()

def print_normalization_stats(original_data, normalized_data):
    """打印归一化统计信息"""
    print("\n=== 归一化统计信息 ===")
    for filename in original_data.keys():
        orig_values = original_data[filename][:, 1]
        norm_values = normalized_data[filename][:, 1]

        print(f"\n文件: {filename}")
        print(f"  原始数据范围: [{orig_values.min():.3f}, {orig_values.max():.3f}]")
        print(f"  归一化后范围: [{norm_values.min():.3f}, {norm_values.max():.3f}]")
        print(f"  原始数据标准差: {orig_values.std():.3f}")
        print(f"  归一化后标准差: {norm_values.std():.3f}")

def compare_trimming_methods(folder_path, sample_file=None):
    """对比固定裁剪和自适应裁剪的效果

    Args:
        folder_path: 文件夹路径
        sample_file: 指定要对比的文件，如果为None则使用第一个找到的文件
    """
    # 找到一个示例文件
    if sample_file is None:
        for filename in os.listdir(folder_path):
            if filename.endswith(".asc"):
                sample_file = os.path.join(folder_path, filename)
                break

    if sample_file is None:
        print("未找到OTDR文件进行对比")
        return

    print(f"\n=== 裁剪方法对比 (文件: {os.path.basename(sample_file)}) ===")

    # 使用固定裁剪
    fixed_data = process_otdr_file(sample_file, normalize=False, adaptive_trimming=False)

    # 使用自适应裁剪
    adaptive_data = process_otdr_file(sample_file, normalize=False, adaptive_trimming=True)

    print(f"固定裁剪结果: {len(fixed_data)} 个数据点")
    print(f"自适应裁剪结果: {len(adaptive_data)} 个数据点")

    if len(fixed_data) > 0 and len(adaptive_data) > 0:
        print(f"固定裁剪位置范围: {fixed_data[0,0]:.3f} - {fixed_data[-1,0]:.3f} km")
        print(f"自适应裁剪位置范围: {adaptive_data[0,0]:.3f} - {adaptive_data[-1,0]:.3f} km")

        # 绘制对比图
        plt.figure(figsize=(14, 8))

        # 读取原始数据用于对比
        with open(sample_file, 'r') as f:
            content = f.read()

        data_lines = []
        in_data_section = False
        for line in content.split('\n'):
            if "POS(km)" in line:
                in_data_section = True
                continue
            if in_data_section and line.strip():
                if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):
                    data_lines.append(line)

        raw_data = []
        for line in data_lines:
            pos, val = map(float, line.strip().split())
            raw_data.append([pos, val])
        raw_data = np.array(raw_data)

        # 绘制三条曲线
        plt.plot(raw_data[:, 0], raw_data[:, 1], 'lightgray', alpha=0.7, linewidth=1, label='原始数据')
        plt.plot(fixed_data[:, 0], fixed_data[:, 1], 'blue', linewidth=2, label='固定裁剪 (前10点+后50点)')
        plt.plot(adaptive_data[:, 0], adaptive_data[:, 1], 'red', linewidth=2, label='自适应裁剪 (智能检测)')

        plt.title(f'裁剪方法对比 - {os.path.basename(sample_file)}', pad=20)
        plt.xlabel('Position (km)')
        plt.ylabel('Value (dB)')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.tight_layout()
        plt.show()

        return fixed_data, adaptive_data

    return None, None


# 主函数
if __name__ == "__main__":
    print("\n=== 开始文件处理程序 ===")

    # 尝试加载同缆组配置文件
    print("\n=== 加载同缆组配置 ===")
    config_loaded = False

    # 优先尝试加载JSON配置文件
    if os.path.exists("cable_groups.json"):
        config_loaded = load_cable_groups_from_json("cable_groups.json")
    # 如果JSON不存在，尝试加载CSV配置文件
    elif os.path.exists("cable_groups.csv"):
        config_loaded = load_cable_groups_from_csv("cable_groups.csv")

    if not config_loaded:
        print("警告: 未找到配置文件，将使用空的同缆组配置")
        print("请创建 cable_groups.json 或 cable_groups.csv 配置文件")

    # 显示加载的同缆组信息
    if CABLE_GROUPS:
        print(f"已加载同缆组信息:")
        for group_id, files in CABLE_GROUPS.items():
            print(f"  组 {group_id}: {len(files)} 个文件")

    folder_path = "C:/Users/<USER>/Desktop/干线测试"

    print("\n=== 处理OTDR数据 ===")
    # 使用自适应裁剪处理原始数据（不归一化）
    print("处理原始数据（智能去除首尾盲区和噪声）...")
    original_data = process_folder(folder_path, normalize=False, adaptive_trimming=True)

    # 使用自适应裁剪处理归一化数据
    print("处理归一化数据（智能去除首尾盲区和噪声 + Min-Max归一化）...")
    normalized_data = process_folder(folder_path, normalize=True, adaptive_trimming=True)

    if not original_data:
        print("未找到有效的OTDR文件！")
    else:
        print(f"成功读取 {len(original_data)} 个OTDR文件")

        # 显示归一化统计信息
        print_normalization_stats(original_data, normalized_data)

        # 对比显示原始数据和归一化数据
        print("\n=== 显示对比图表 ===")
        plot_comparison(original_data, normalized_data)